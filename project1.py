import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from statsmodels.tsa.statespace.sarimax import SARIMAX
from sklearn.metrics import mean_squared_error, mean_absolute_error

def main():
    # 1. Load data
    df = pd.read_csv('philippines_inflation.csv', parse_dates=['Date'], index_col='Date')
    df = df.asfreq('MS')  # Monthly Start frequency
    df = df.fillna(method='ffill')  # Fill missing values if any

    # 2. Visualize the inflation data
    df['Inflation'].plot(title='Monthly Inflation Rate in the Philippines', figsize=(10, 4))
    plt.ylabel('Inflation Rate (%)')
    plt.grid()
    plt.show()

    # 3. Split the data
    train = df.iloc[:-12]   # Use all but the last 12 months for training
    test = df.iloc[-12:]    # Last 12 months for testing

    # 4. Fit SARIMAX model
    model = SARIMAX(train['Inflation'],
                    order=(1,1,1),
                    seasonal_order=(1,1,1,12),
                    enforce_stationarity=False,
                    enforce_invertibility=False)
    model_fit = model.fit(disp=False)

    # 5. Forecast
    forecast = model_fit.predict(start=test.index[0], end=test.index[-1], dynamic=False)

    # 6. Plot actual vs forecast
    plt.figure(figsize=(10, 4))
    plt.plot(train.index, train['Inflation'], label='Train')
    plt.plot(test.index, test['Inflation'], label='Actual')
    plt.plot(test.index, forecast, label='Forecast')
    plt.title('SARIMAX Inflation Forecast')
    plt.legend()
    plt.grid()
    plt.show()

    # 7. Evaluation Metrics
    rmse = np.sqrt(mean_squared_error(test['Inflation'], forecast))
    mae = mean_absolute_error(test['Inflation'], forecast)
    mape = np.mean(np.abs((test['Inflation'] - forecast) / test['Inflation'])) * 100

    print(f"Root Mean Squared Error (RMSE): {rmse:.4f}")
    print(f"Mean Absolute Error (MAE): {mae:.4f}")
    print(f"Mean Absolute Percentage Error (MAPE): {mape:.2f}%")

if __name__ == "__main__":
    main()